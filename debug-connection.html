<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connection Debug Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Portfolio Contact Form Debug Tool</h1>
    <p>This tool helps diagnose connection issues with your contact form.</p>

    <div class="test-section">
        <h3>1. Health Check Test</h3>
        <p>Tests if the server is running and accessible.</p>
        <button onclick="testHealthCheck()">Test Health Check</button>
        <div id="health-result"></div>
    </div>

    <div class="test-section">
        <h3>2. CORS Test</h3>
        <p>Tests if CORS is properly configured for your domain.</p>
        <button onclick="testCORS()">Test CORS</button>
        <div id="cors-result"></div>
    </div>

    <div class="test-section">
        <h3>3. Contact Form Test</h3>
        <p>Tests the actual contact form submission.</p>
        <button onclick="testContactForm()">Test Contact Form</button>
        <div id="contact-result"></div>
    </div>

    <div class="test-section">
        <h3>4. Environment Info</h3>
        <div id="env-info" class="info">
            <strong>Current URL:</strong> <span id="current-url"></span><br>
            <strong>User Agent:</strong> <span id="user-agent"></span><br>
            <strong>Timestamp:</strong> <span id="timestamp"></span>
        </div>
    </div>

    <script>
        // Display environment info
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('user-agent').textContent = navigator.userAgent;
        document.getElementById('timestamp').textContent = new Date().toISOString();

        async function testHealthCheck() {
            const resultDiv = document.getElementById('health-result');
            resultDiv.innerHTML = '<div class="info">Testing health check...</div>';

            try {
                const response = await fetch('/api/health');
                const data = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ Health check passed!
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ Health check failed: ${response.status} ${response.statusText}
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ Health check error: ${error.message}
                        <br>This usually indicates a network connectivity issue.
                    </div>
                `;
            }
        }

        async function testCORS() {
            const resultDiv = document.getElementById('cors-result');
            resultDiv.innerHTML = '<div class="info">Testing CORS...</div>';

            try {
                const response = await fetch('/api/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ CORS is working correctly!
                            <br>Origin: ${window.location.origin}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ CORS test failed: ${response.status} ${response.statusText}
                        </div>
                    `;
                }
            } catch (error) {
                if (error.message.includes('CORS')) {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ CORS Error: ${error.message}
                            <br>Your domain may not be in the allowed origins list.
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ Network Error: ${error.message}
                        </div>
                    `;
                }
            }
        }

        async function testContactForm() {
            const resultDiv = document.getElementById('contact-result');
            resultDiv.innerHTML = '<div class="info">Testing contact form submission...</div>';

            const testData = {
                name: 'Debug Test User',
                email: '<EMAIL>',
                subject: 'Debug Test Message',
                message: 'This is a test message from the debug tool to verify the contact form is working properly.'
            };

            try {
                const response = await fetch('/api/contact', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ Contact form test passed!
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ Contact form test failed: ${response.status} ${response.statusText}
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ Contact form error: ${error.message}
                        <br>This is the same error your users are experiencing.
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
